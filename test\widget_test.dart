// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:hypothesis_helper/main.dart';

void main() {
  testWidgets('App starts correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const HypothesisHelperApp());

    // Verify that the app starts with the calculator screen
    expect(find.text('假设检验计算器'), findsOneWidget);
    expect(find.text('计算器'), findsOneWidget);
    expect(find.text('学习模块'), findsOneWidget);
  });
}
